"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedCodeAnalysis = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const glob = __importStar(require("glob"));
const vscode = __importStar(require("vscode"));
class AdvancedCodeAnalysis {
    constructor(workspaceRoot) {
        this.analysisCache = new Map();
        this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
    }
    // Comprehensive project analysis
    async analyzeProject() {
        const files = await this.getProjectFiles();
        const metrics = await this.calculateCodeMetrics(files);
        const refactoringOpportunities = await this.identifyRefactoringOpportunities(files);
        const securityAnalysis = await this.performSecurityAnalysis(files);
        const recommendations = await this.generateRecommendations(metrics, refactoringOpportunities, securityAnalysis);
        return {
            metrics,
            refactoringOpportunities,
            securityAnalysis,
            recommendations
        };
    }
    // Calculate comprehensive code metrics
    async calculateCodeMetrics(files) {
        let totalComplexity = 0;
        let totalMaintainability = 0;
        const codeSmells = [];
        for (const file of files) {
            const analysis = await this.analyzeFile(file);
            totalComplexity += analysis.complexity;
            totalMaintainability += analysis.maintainability;
            codeSmells.push(...analysis.codeSmells);
        }
        const dependencies = await this.analyzeDependencies();
        const performance = await this.analyzePerformance();
        return {
            complexity: totalComplexity / files.length,
            maintainability: totalMaintainability / files.length,
            testCoverage: await this.calculateTestCoverage(),
            codeSmells,
            dependencies,
            performance
        };
    }
    // Analyze individual file
    async analyzeFile(filePath) {
        const fullPath = path.resolve(this.workspaceRoot, filePath);
        const content = await fs.readFile(fullPath, 'utf-8');
        const lines = content.split('\n');
        const complexity = this.calculateCyclomaticComplexity(content);
        const maintainability = this.calculateMaintainabilityIndex(content, complexity);
        const codeSmells = this.detectCodeSmells(filePath, content, lines);
        return {
            complexity,
            maintainability,
            codeSmells
        };
    }
    // Calculate cyclomatic complexity
    calculateCyclomaticComplexity(content) {
        // Count decision points: if, while, for, case, catch, &&, ||, ?
        const patterns = [
            /\bif\s*\(/g,
            /\bwhile\s*\(/g,
            /\bfor\s*\(/g,
            /\bcase\s+/g,
            /\bcatch\s*\(/g,
            /&&/g,
            /\|\|/g,
            /\?/g
        ];
        let complexity = 1; // Base complexity
        for (const pattern of patterns) {
            const matches = content.match(pattern);
            if (matches) {
                complexity += matches.length;
            }
        }
        return complexity;
    }
    // Calculate maintainability index
    calculateMaintainabilityIndex(content, complexity) {
        const lines = content.split('\n');
        const linesOfCode = lines.filter(line => line.trim().length > 0).length;
        const halsteadVolume = this.calculateHalsteadVolume(content);
        // Simplified maintainability index calculation
        const maintainability = Math.max(0, 171 - 5.2 * Math.log(halsteadVolume) - 0.23 * complexity - 16.2 * Math.log(linesOfCode));
        return Math.min(100, maintainability);
    }
    // Calculate Halstead volume (simplified)
    calculateHalsteadVolume(content) {
        const operators = content.match(/[+\-*/=<>!&|^%~?:;,(){}[\]]/g) || [];
        const operands = content.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g) || [];
        const uniqueOperators = new Set(operators).size;
        const uniqueOperands = new Set(operands).size;
        const totalOperators = operators.length;
        const totalOperands = operands.length;
        const vocabulary = uniqueOperators + uniqueOperands;
        const length = totalOperators + totalOperands;
        return length * Math.log2(vocabulary || 1);
    }
    // Detect code smells
    detectCodeSmells(filePath, content, lines) {
        const smells = [];
        // Long method detection
        const functionMatches = content.match(/function\s+\w+|const\s+\w+\s*=\s*\(/g);
        if (functionMatches) {
            for (let i = 0; i < lines.length; i++) {
                if (lines[i].includes('function') || lines[i].includes('=>')) {
                    let braceCount = 0;
                    let functionLength = 0;
                    for (let j = i; j < lines.length; j++) {
                        braceCount += (lines[j].match(/{/g) || []).length;
                        braceCount -= (lines[j].match(/}/g) || []).length;
                        functionLength++;
                        if (braceCount === 0 && functionLength > 50) {
                            smells.push({
                                type: 'complexity',
                                severity: 'medium',
                                file: filePath,
                                line: i + 1,
                                description: `Function is too long (${functionLength} lines)`,
                                suggestion: 'Consider breaking this function into smaller, more focused functions'
                            });
                            break;
                        }
                    }
                }
            }
        }
        // Duplicate code detection (simplified)
        const duplicateThreshold = 5;
        for (let i = 0; i < lines.length - duplicateThreshold; i++) {
            const block = lines.slice(i, i + duplicateThreshold).join('\n');
            const remaining = lines.slice(i + duplicateThreshold).join('\n');
            if (remaining.includes(block) && block.trim().length > 50) {
                smells.push({
                    type: 'duplication',
                    severity: 'medium',
                    file: filePath,
                    line: i + 1,
                    description: 'Duplicate code block detected',
                    suggestion: 'Extract common code into a reusable function'
                });
            }
        }
        return smells;
    }
    // Get all project files for analysis
    async getProjectFiles() {
        try {
            return await glob('src/**/*.{ts,tsx,js,jsx}', {
                cwd: this.workspaceRoot,
                ignore: ['node_modules/**', 'dist/**', 'out/**', '**/*.test.*', '**/*.spec.*'],
                nodir: true
            });
        }
        catch (error) {
            return [];
        }
    }
    // Analyze dependencies
    async analyzeDependencies() {
        const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
        let external = [];
        if (await fs.pathExists(packageJsonPath)) {
            const packageJson = await fs.readJson(packageJsonPath);
            external = Object.keys({
                ...packageJson.dependencies,
                ...packageJson.devDependencies
            });
        }
        return {
            internal: [], // Would be populated by analyzing import statements
            external,
            circular: [], // Would be detected by dependency graph analysis
            unused: [], // Would be detected by usage analysis
            outdated: [] // Would be detected by version checking
        };
    }
    // Analyze performance metrics
    async analyzePerformance() {
        return {
            bundleSize: 0, // Would be calculated from build output
            loadTime: 0, // Would be measured from performance tests
            memoryUsage: 0, // Would be measured from runtime analysis
            potentialOptimizations: [
                'Consider code splitting for large bundles',
                'Implement lazy loading for components',
                'Optimize image assets',
                'Use tree shaking to eliminate dead code'
            ]
        };
    }
    // Calculate test coverage
    async calculateTestCoverage() {
        // This would integrate with coverage tools like Istanbul/NYC
        return 0; // Placeholder
    }
    // Identify refactoring opportunities
    async identifyRefactoringOpportunities(files) {
        const opportunities = [];
        // This would analyze code patterns and suggest refactoring
        // For now, return placeholder opportunities
        return opportunities;
    }
    // Perform security analysis
    async performSecurityAnalysis(files) {
        const vulnerabilities = [];
        // This would scan for common security issues
        // For now, return basic analysis
        return {
            vulnerabilities,
            riskScore: 0,
            recommendations: [
                'Keep dependencies up to date',
                'Use HTTPS for all external requests',
                'Validate all user inputs',
                'Implement proper authentication and authorization'
            ]
        };
    }
    // Generate recommendations based on analysis
    async generateRecommendations(metrics, refactoring, security) {
        const recommendations = [];
        if (metrics.complexity > 10) {
            recommendations.push('Consider reducing code complexity by breaking down large functions');
        }
        if (metrics.maintainability < 70) {
            recommendations.push('Improve code maintainability by adding documentation and reducing complexity');
        }
        if (metrics.codeSmells.length > 10) {
            recommendations.push('Address code smells to improve code quality');
        }
        if (security.riskScore > 5) {
            recommendations.push('Address security vulnerabilities to reduce risk');
        }
        return recommendations;
    }
}
exports.AdvancedCodeAnalysis = AdvancedCodeAnalysis;
//# sourceMappingURL=AdvancedCodeAnalysis.js.map