"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntelligentCodeGeneration = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const vscode = __importStar(require("vscode"));
class IntelligentCodeGeneration {
    constructor(workspaceRoot) {
        this.templates = new Map();
        this.context = null;
        this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        this.initializeTemplates();
        this.analyzeProjectContext();
    }
    // Generate code based on request
    async generateCode(request) {
        const template = this.selectBestTemplate(request);
        const context = request.context || this.context || this.getDefaultContext();
        const generatedFiles = await this.generateFiles(template, request, context);
        const dependencies = this.extractDependencies(template, request);
        const instructions = this.generateInstructions(request, generatedFiles);
        const tests = await this.generateTests(request, context);
        return {
            files: generatedFiles,
            dependencies,
            instructions,
            tests
        };
    }
    // Generate React component
    async generateReactComponent(name, options = {}) {
        const request = {
            type: 'component',
            name,
            description: `React component ${name}`,
            options,
            context: this.context || this.getDefaultContext()
        };
        return this.generateCode(request);
    }
    // Generate service class
    async generateService(name, options = {}) {
        const request = {
            type: 'service',
            name,
            description: `Service class ${name}`,
            options,
            context: this.context || this.getDefaultContext()
        };
        return this.generateCode(request);
    }
    // Generate utility functions
    async generateUtility(name, options) {
        const request = {
            type: 'utility',
            name,
            description: `Utility functions ${name}`,
            options,
            context: this.context || this.getDefaultContext()
        };
        return this.generateCode(request);
    }
    // Initialize built-in templates
    initializeTemplates() {
        // React Component Template
        this.templates.set('react-component', {
            name: 'React Component',
            description: 'Functional React component with TypeScript',
            language: 'typescript',
            framework: 'react',
            template: `import React{{#if withProps}}, { FC }{{/if}}{{#if withState}}, { useState }{{/if}} from 'react';
{{#if withStyles}}import './{{name}}.css';{{/if}}

{{#if withProps}}
interface {{name}}Props {
  // Define props here
}
{{/if}}

{{#if withProps}}
export const {{name}}: FC<{{name}}Props> = ({{#if withProps}}props{{/if}}) => {
{{else}}
export const {{name}} = () => {
{{/if}}
{{#if withState}}
  const [state, setState] = useState();
{{/if}}

  return (
    <div className="{{kebabCase name}}">
      <h1>{{name}} Component</h1>
    </div>
  );
};

export default {{name}};`,
            variables: [
                { name: 'name', type: 'string', description: 'Component name', required: true },
                { name: 'withProps', type: 'boolean', description: 'Include props interface', default: false, required: false },
                { name: 'withState', type: 'boolean', description: 'Include state hook', default: false, required: false },
                { name: 'withStyles', type: 'boolean', description: 'Include CSS import', default: false, required: false }
            ]
        });
        // Service Template
        this.templates.set('service', {
            name: 'Service Class',
            description: 'TypeScript service class',
            language: 'typescript',
            template: `{{#if withInterface}}
export interface I{{name}}Service {
{{#each methods}}
  {{this}}(): Promise<any>;
{{/each}}
}
{{/if}}

export class {{name}}Service{{#if withInterface}} implements I{{name}}Service{{/if}} {
  private baseUrl: string;

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || '';
  }

{{#each methods}}
  async {{this}}(): Promise<any> {
    try {
      // Implementation here
      throw new Error('Method not implemented');
    } catch (error) {
      console.error('Error in {{this}}:', error);
      throw error;
    }
  }

{{/each}}
}

export default {{name}}Service;`,
            variables: [
                { name: 'name', type: 'string', description: 'Service name', required: true },
                { name: 'withInterface', type: 'boolean', description: 'Include interface', default: true, required: false },
                { name: 'methods', type: 'array', description: 'Service methods', default: ['getData', 'saveData'], required: false }
            ]
        });
        // Utility Template
        this.templates.set('utility', {
            name: 'Utility Functions',
            description: 'TypeScript utility functions',
            language: 'typescript',
            template: `/**
 * {{description}}
 */

{{#each functions}}
export const {{this}} = ({{#if ../pure}}input: any{{/if}}) => {
  // Implementation here
  throw new Error('Function not implemented');
};

{{/each}}

export default {
{{#each functions}}
  {{this}}{{#unless @last}},{{/unless}}
{{/each}}
};`,
            variables: [
                { name: 'functions', type: 'array', description: 'Function names', required: true },
                { name: 'pure', type: 'boolean', description: 'Pure functions', default: true, required: false }
            ]
        });
    }
    // Analyze project context
    async analyzeProjectContext() {
        try {
            const packageJsonPath = path.join(this.workspaceRoot, 'package.json');
            let framework = 'vanilla';
            let language = 'javascript';
            let dependencies = [];
            if (await fs.pathExists(packageJsonPath)) {
                const packageJson = await fs.readJson(packageJsonPath);
                dependencies = Object.keys({
                    ...packageJson.dependencies,
                    ...packageJson.devDependencies
                });
                if (dependencies.includes('react'))
                    framework = 'react';
                if (dependencies.includes('vue'))
                    framework = 'vue';
                if (dependencies.includes('angular'))
                    framework = 'angular';
                if (dependencies.includes('typescript'))
                    language = 'typescript';
            }
            // Analyze code conventions
            const conventions = await this.analyzeCodeConventions();
            this.context = {
                projectType: framework === 'vanilla' ? 'library' : 'application',
                framework,
                language,
                conventions,
                existingPatterns: await this.detectPatterns(),
                dependencies
            };
        }
        catch (error) {
            console.error('Failed to analyze project context:', error);
            this.context = this.getDefaultContext();
        }
    }
    // Select best template for request
    selectBestTemplate(request) {
        const templateKey = `${request.type}${request.context.framework ? `-${request.context.framework}` : ''}`;
        let template = this.templates.get(templateKey);
        if (!template) {
            template = this.templates.get(request.type);
        }
        if (!template) {
            template = this.templates.get('utility'); // Fallback
        }
        return template;
    }
    // Generate files from template
    async generateFiles(template, request, context) {
        const files = [];
        // Generate main file
        const mainContent = this.processTemplate(template.template, {
            ...request.options,
            name: request.name,
            description: request.description
        });
        const extension = context.language === 'typescript' ? '.ts' : '.js';
        const componentExtension = context.framework === 'react' ?
            (context.language === 'typescript' ? '.tsx' : '.jsx') : extension;
        const fileName = request.type === 'component' ?
            `${request.name}${componentExtension}` :
            `${request.name}${extension}`;
        const targetPath = request.targetPath || this.getDefaultPath(request.type, fileName);
        files.push({
            path: targetPath,
            content: this.formatCode(mainContent, context),
            type: 'main',
            description: `Main ${request.type} file`
        });
        // Generate styles if needed
        if (request.type === 'component' && request.options.withStyles) {
            const styleContent = this.generateStyleFile(request.name);
            files.push({
                path: path.join(path.dirname(targetPath), `${request.name}.css`),
                content: styleContent,
                type: 'style',
                description: 'Component styles'
            });
        }
        return files;
    }
    // Generate test files
    async generateTests(request, context) {
        if (!request.options.withTests)
            return [];
        const testContent = this.generateTestContent(request, context);
        const extension = context.language === 'typescript' ? '.test.ts' : '.test.js';
        return [{
                path: `${request.name}${extension}`,
                content: testContent,
                type: 'test',
                description: 'Unit tests'
            }];
    }
    // Process template with variables
    processTemplate(template, variables) {
        let processed = template;
        // Simple template processing (in production, use a proper template engine)
        for (const [key, value] of Object.entries(variables)) {
            const regex = new RegExp(`{{${key}}}`, 'g');
            processed = processed.replace(regex, String(value));
        }
        // Handle conditionals
        processed = processed.replace(/{{#if (\w+)}}([\s\S]*?){{\/if}}/g, (match, condition, content) => {
            return variables[condition] ? content : '';
        });
        // Handle loops
        processed = processed.replace(/{{#each (\w+)}}([\s\S]*?){{\/each}}/g, (match, arrayName, content) => {
            const array = variables[arrayName];
            if (!Array.isArray(array))
                return '';
            return array.map(item => content.replace(/{{this}}/g, item)).join('');
        });
        // Handle kebab-case helper
        processed = processed.replace(/{{kebabCase (\w+)}}/g, (match, varName) => {
            const value = variables[varName];
            return this.toKebabCase(String(value));
        });
        return processed;
    }
    // Format code according to project conventions
    formatCode(code, context) {
        let formatted = code;
        // Apply indentation
        if (context.conventions.indentation === 'tabs') {
            formatted = formatted.replace(/  /g, '\t');
        }
        // Apply quote style
        if (context.conventions.quotes === 'double') {
            formatted = formatted.replace(/'/g, '"');
        }
        return formatted;
    }
    // Helper methods
    getDefaultContext() {
        return {
            projectType: 'application',
            framework: 'react',
            language: 'typescript',
            conventions: {
                naming: 'camelCase',
                indentation: 'spaces',
                quotes: 'single'
            },
            existingPatterns: [],
            dependencies: []
        };
    }
    getDefaultPath(type, fileName) {
        const basePaths = {
            component: 'src/components',
            service: 'src/services',
            utility: 'src/utils',
            test: 'src/__tests__',
            config: 'src/config'
        };
        const basePath = basePaths[type] || 'src';
        return path.join(basePath, fileName);
    }
    async analyzeCodeConventions() {
        // Simplified convention analysis
        return {
            naming: 'camelCase',
            indentation: 'spaces',
            quotes: 'single'
        };
    }
    async detectPatterns() {
        // Detect existing code patterns
        return [];
    }
    extractDependencies(template, request) {
        return template.dependencies || [];
    }
    generateInstructions(request, files) {
        const instructions = [
            `Generated ${request.type}: ${request.name}`,
            `Files created: ${files.length}`
        ];
        if (files.some(f => f.type === 'style')) {
            instructions.push('Remember to import the CSS file in your component');
        }
        return instructions;
    }
    generateStyleFile(componentName) {
        const className = this.toKebabCase(componentName);
        return `.${className} {
  /* Add your styles here */
}`;
    }
    generateTestContent(request, context) {
        const isReact = context.framework === 'react';
        const testFramework = isReact ? 'react-testing-library' : 'jest';
        if (isReact && request.type === 'component') {
            return `import React from 'react';
import { render, screen } from '@testing-library/react';
import ${request.name} from './${request.name}';

describe('${request.name}', () => {
  it('renders without crashing', () => {
    render(<${request.name} />);
  });

  it('displays the component name', () => {
    render(<${request.name} />);
    expect(screen.getByText('${request.name} Component')).toBeInTheDocument();
  });
});`;
        }
        return `import ${request.name} from './${request.name}';

describe('${request.name}', () => {
  it('should be defined', () => {
    expect(${request.name}).toBeDefined();
  });
});`;
    }
    toKebabCase(str) {
        return str
            .replace(/([a-z])([A-Z])/g, '$1-$2')
            .toLowerCase();
    }
    // Enhanced code generation for Microchip development
    async generateMicrochipCode(request) {
        const files = [];
        const dependencies = [];
        const instructions = [];
        switch (request.type) {
            case 'service':
                if (request.name.toLowerCase().includes('microchip') || request.name.toLowerCase().includes('api')) {
                    files.push(this.generateMicrochipAPIService(request));
                    dependencies.push('@types/node', 'axios');
                    instructions.push('Configure API endpoints in environment variables');
                    instructions.push('Add error handling for network requests');
                }
                break;
            case 'utility':
                if (request.name.toLowerCase().includes('parser') || request.name.toLowerCase().includes('data')) {
                    files.push(this.generateDataParserUtility(request));
                    dependencies.push('zod');
                    instructions.push('Validate input data schemas');
                }
                break;
            case 'config':
                files.push(this.generateConfigurationFile(request));
                instructions.push('Update environment variables');
                instructions.push('Configure build settings');
                break;
        }
        return { files, dependencies, instructions };
    }
    generateMicrochipAPIService(request) {
        const content = `import axios, { AxiosResponse } from 'axios';

export interface ${request.name}Config {
  apiKey: string;
  baseURL: string;
  timeout?: number;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

export class ${request.name} {
  private config: ${request.name}Config;
  private axiosInstance;

  constructor(config: ${request.name}Config) {
    this.config = config;
    this.axiosInstance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      headers: {
        'Authorization': \`Bearer \${config.apiKey}\`,
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(\`Making request to: \${config.url}\`);
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  async sendRequest<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    try {
      const response: AxiosResponse<T> = await this.axiosInstance.post(endpoint, data);

      return {
        success: true,
        data: response.data,
        timestamp: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.axiosInstance.get('/health');
      return response.status === 200;
    } catch {
      return false;
    }
  }
}`;
        return {
            path: `src/services/${request.name}.ts`,
            content,
            description: `Microchip API service with error handling and interceptors`
        };
    }
    generateDataParserUtility(request) {
        const content = `import { z } from 'zod';

// Data validation schemas
export const ${request.name}Schema = z.object({
  id: z.string(),
  timestamp: z.date(),
  data: z.record(z.unknown()),
  metadata: z.object({
    source: z.string(),
    version: z.string().optional()
  }).optional()
});

export type ${request.name}Data = z.infer<typeof ${request.name}Schema>;

export interface ParseResult<T> {
  success: boolean;
  data?: T;
  errors?: string[];
}

export class ${request.name} {
  static parse<T>(input: unknown, schema: z.ZodSchema<T>): ParseResult<T> {
    try {
      const data = schema.parse(input);
      return { success: true, data };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          errors: error.errors.map(e => \`\${e.path.join('.')}: \${e.message}\`)
        };
      }
      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown parsing error']
      };
    }
  }

  static validateMicrochipData(input: unknown): ParseResult<${request.name}Data> {
    return this.parse(input, ${request.name}Schema);
  }

  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/[\\x00-\\x1F\\x7F]/g, ''); // Remove control characters
  }

  static formatOutput(data: any): string {
    try {
      return JSON.stringify(data, null, 2);
    } catch {
      return String(data);
    }
  }
}`;
        return {
            path: `src/utils/${request.name}.ts`,
            content,
            description: `Data parser utility with validation and sanitization`
        };
    }
    generateConfigurationFile(request) {
        const content = `export interface AppConfig {
  // API Configuration
  api: {
    microchipApiKey: string;
    openaiApiKey: string;
    baseURL: string;
    timeout: number;
  };

  // Development Settings
  development: {
    enableLogging: boolean;
    debugMode: boolean;
    hotReload: boolean;
  };

  // Feature Flags
  features: {
    advancedAgent: boolean;
    vectorSearch: boolean;
    codeGeneration: boolean;
    errorDetection: boolean;
  };

  // UI Settings
  ui: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    autoSave: boolean;
  };
}

export const defaultConfig: AppConfig = {
  api: {
    microchipApiKey: process.env.MICROCHIP_API_KEY || '',
    openaiApiKey: process.env.OPENAI_API_KEY || '',
    baseURL: process.env.API_BASE_URL || 'http://localhost:3001',
    timeout: 30000
  },
  development: {
    enableLogging: process.env.NODE_ENV === 'development',
    debugMode: process.env.DEBUG === 'true',
    hotReload: process.env.NODE_ENV === 'development'
  },
  features: {
    advancedAgent: true,
    vectorSearch: true,
    codeGeneration: true,
    errorDetection: true
  },
  ui: {
    theme: 'auto',
    language: 'en',
    autoSave: true
  }
};

export class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig;

  private constructor() {
    this.config = { ...defaultConfig };
    this.loadFromEnvironment();
  }

  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private loadFromEnvironment(): void {
    // Load configuration from environment variables
    if (process.env.MICROCHIP_API_KEY) {
      this.config.api.microchipApiKey = process.env.MICROCHIP_API_KEY;
    }
    if (process.env.OPENAI_API_KEY) {
      this.config.api.openaiApiKey = process.env.OPENAI_API_KEY;
    }
  }

  getConfig(): AppConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<AppConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  isFeatureEnabled(feature: keyof AppConfig['features']): boolean {
    return this.config.features[feature];
  }
}`;
        return {
            path: `src/config/${request.name}.ts`,
            content,
            description: `Application configuration with environment variable support`
        };
    }
    // Enhanced template system for VS Code extensions
    async generateVSCodeExtensionCode(request) {
        const files = [];
        const dependencies = [];
        const instructions = [];
        switch (request.type) {
            case 'component':
                if (request.name.toLowerCase().includes('webview')) {
                    files.push(this.generateWebviewComponent(request));
                    dependencies.push('react', 'react-dom', '@types/react', '@types/react-dom');
                    instructions.push('Configure webview in package.json');
                    instructions.push('Add webview provider registration');
                }
                break;
            case 'service':
                if (request.name.toLowerCase().includes('provider')) {
                    files.push(this.generateWebviewProvider(request));
                    dependencies.push('@types/vscode');
                    instructions.push('Register provider in extension.ts');
                }
                break;
        }
        return { files, dependencies, instructions };
    }
    generateWebviewComponent(request) {
        const content = `import React, { useState, useEffect } from 'react';
import './styles.css';

interface ${request.name}Props {
  onMessage?: (message: any) => void;
  initialData?: any;
}

interface VSCodeAPI {
  postMessage(message: any): void;
  getState(): any;
  setState(state: any): void;
}

declare global {
  interface Window {
    acquireVsCodeApi(): VSCodeAPI;
  }
}

export const ${request.name}: React.FC<${request.name}Props> = ({
  onMessage,
  initialData
}) => {
  const [vscode] = useState(() => window.acquireVsCodeApi());
  const [data, setData] = useState(initialData || {});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Listen for messages from the extension
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;

      switch (message.type) {
        case 'update':
          setData(message.data);
          break;
        case 'loading':
          setLoading(message.loading);
          break;
        default:
          if (onMessage) {
            onMessage(message);
          }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [onMessage]);

  const sendMessage = (type: string, payload?: any) => {
    vscode.postMessage({ type, payload });
  };

  const handleAction = (action: string) => {
    setLoading(true);
    sendMessage('action', { action, timestamp: Date.now() });
  };

  return (
    <div className="${this.toKebabCase(request.name)}">
      <header className="header">
        <h1>${request.name}</h1>
        {loading && <div className="loading-spinner">Loading...</div>}
      </header>

      <main className="content">
        <div className="actions">
          <button
            onClick={() => handleAction('refresh')}
            disabled={loading}
            className="btn btn-primary"
          >
            Refresh
          </button>
          <button
            onClick={() => handleAction('settings')}
            disabled={loading}
            className="btn btn-secondary"
          >
            Settings
          </button>
        </div>

        <div className="data-display">
          <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
      </main>
    </div>
  );
};

export default ${request.name};`;
        return {
            path: `src/webview/components/${request.name}.tsx`,
            content,
            description: `React webview component with VS Code API integration`
        };
    }
    generateWebviewProvider(request) {
        const content = `import * as vscode from 'vscode';
import * as path from 'path';

export class ${request.name} implements vscode.WebviewViewProvider {
  public static readonly viewType = '${this.toKebabCase(request.name)}';
  private _view?: vscode.WebviewView;

  constructor(
    private readonly _extensionUri: vscode.Uri,
    private readonly _context: vscode.ExtensionContext
  ) {}

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken,
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [this._extensionUri]
    };

    webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      message => {
        switch (message.type) {
          case 'action':
            this.handleAction(message.payload);
            break;
          case 'error':
            vscode.window.showErrorMessage(message.message);
            break;
          case 'info':
            vscode.window.showInformationMessage(message.message);
            break;
        }
      },
      undefined,
      this._context.subscriptions
    );

    // Send initial data
    this.sendMessage('update', {
      timestamp: Date.now(),
      workspace: vscode.workspace.name
    });
  }

  private async handleAction(payload: any) {
    try {
      this.sendMessage('loading', { loading: true });

      switch (payload.action) {
        case 'refresh':
          await this.refreshData();
          break;
        case 'settings':
          await this.openSettings();
          break;
        default:
          vscode.window.showWarningMessage(\`Unknown action: \${payload.action}\`);
      }
    } catch (error) {
      vscode.window.showErrorMessage(\`Action failed: \${error instanceof Error ? error.message : 'Unknown error'}\`);
    } finally {
      this.sendMessage('loading', { loading: false });
    }
  }

  private async refreshData() {
    // Implement data refresh logic
    const data = {
      timestamp: Date.now(),
      workspace: vscode.workspace.name,
      activeFile: vscode.window.activeTextEditor?.document.fileName
    };

    this.sendMessage('update', data);
  }

  private async openSettings() {
    await vscode.commands.executeCommand('workbench.action.openSettings', '${this.toKebabCase(request.name)}');
  }

  private sendMessage(type: string, data?: any) {
    if (this._view) {
      this._view.webview.postMessage({ type, data });
    }
  }

  private _getHtmlForWebview(webview: vscode.Webview) {
    const scriptUri = webview.asWebviewUri(
      vscode.Uri.joinPath(this._extensionUri, 'dist', 'webview.js')
    );
    const styleUri = webview.asWebviewUri(
      vscode.Uri.joinPath(this._extensionUri, 'dist', 'webview.css')
    );

    const nonce = this.getNonce();

    return \`<!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src \${webview.cspSource}; script-src 'nonce-\${nonce}';">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="\${styleUri}" rel="stylesheet">
        <title>${request.name}</title>
      </head>
      <body>
        <div id="root"></div>
        <script nonce="\${nonce}" src="\${scriptUri}"></script>
      </body>
      </html>\`;
  }

  private getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }
}`;
        return {
            path: `src/providers/${request.name}.ts`,
            content,
            description: `VS Code webview provider with message handling`
        };
    }
}
exports.IntelligentCodeGeneration = IntelligentCodeGeneration;
//# sourceMappingURL=IntelligentCodeGeneration.js.map