"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntelligentSearch = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const glob = __importStar(require("glob"));
const vscode = __importStar(require("vscode"));
class IntelligentSearch {
    constructor(workspaceRoot) {
        this.indexCache = new Map();
        this.lastIndexTime = 0;
        this.INDEX_REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes
        this.workspaceRoot = workspaceRoot || vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd();
        this.initializeIndex();
    }
    // Multi-modal search with intelligent ranking
    async search(query, options = {}) {
        const defaultOptions = {
            includeTests: false,
            includeNodeModules: false,
            fileTypes: ['ts', 'tsx', 'js', 'jsx'],
            maxResults: 50,
            fuzzyThreshold: 0.6,
            contextLines: 3,
            ...options
        };
        const results = [];
        // 1. Exact matches
        const exactResults = await this.searchExact(query, defaultOptions);
        results.push(...exactResults);
        // 2. Fuzzy matches
        const fuzzyResults = await this.searchFuzzy(query, defaultOptions);
        results.push(...fuzzyResults);
        // 3. Pattern matches
        const patternResults = await this.searchPattern(query, defaultOptions);
        results.push(...patternResults);
        // 4. Semantic matches (if available)
        const semanticResults = await this.searchSemantic(query, defaultOptions);
        results.push(...semanticResults);
        // Remove duplicates and sort by relevance
        const uniqueResults = this.deduplicateResults(results);
        const rankedResults = this.rankResults(uniqueResults, query);
        return rankedResults.slice(0, defaultOptions.maxResults);
    }
    // Search for code symbols (functions, classes, etc.)
    async searchSymbols(query) {
        await this.ensureIndexFresh();
        const allSymbols = [];
        for (const symbols of this.indexCache.values()) {
            allSymbols.push(...symbols);
        }
        return allSymbols
            .filter(symbol => symbol.name.toLowerCase().includes(query.toLowerCase()) ||
            this.calculateSimilarity(symbol.name.toLowerCase(), query.toLowerCase()) > 0.6)
            .sort((a, b) => {
            const aScore = this.calculateSymbolRelevance(a, query);
            const bScore = this.calculateSymbolRelevance(b, query);
            return bScore - aScore;
        })
            .slice(0, 20);
    }
    // Find definitions and references
    async findDefinition(symbol, currentFile, line) {
        const suggestions = [];
        // Search for symbol definitions
        const symbols = await this.searchSymbols(symbol);
        for (const sym of symbols) {
            if (sym.name === symbol) {
                suggestions.push({
                    type: 'definition',
                    file: sym.file,
                    line: sym.line,
                    description: `Definition of ${sym.type} ${sym.name}`,
                    confidence: 0.9
                });
            }
        }
        // Search for imports/exports
        const importResults = await this.searchPattern(`import.*${symbol}|export.*${symbol}`);
        for (const result of importResults) {
            suggestions.push({
                type: 'reference',
                file: result.file,
                line: result.line,
                description: `Import/Export of ${symbol}`,
                confidence: 0.7
            });
        }
        return suggestions.sort((a, b) => b.confidence - a.confidence);
    }
    // Get related files and suggestions
    async getRelatedFiles(currentFile) {
        const related = [];
        const currentContent = await this.getFileContent(currentFile);
        if (!currentContent)
            return related;
        // Find imported files
        const importMatches = currentContent.match(/import.*from\s+['"]([^'"]+)['"]/g);
        if (importMatches) {
            for (const match of importMatches) {
                const pathMatch = match.match(/['"]([^'"]+)['"]/);
                if (pathMatch) {
                    const importPath = this.resolveImportPath(pathMatch[1], currentFile);
                    if (importPath) {
                        related.push(importPath);
                    }
                }
            }
        }
        // Find files that import this file
        const allFiles = await this.getAllProjectFiles();
        for (const file of allFiles) {
            if (file === currentFile)
                continue;
            const content = await this.getFileContent(file);
            if (content && content.includes(path.basename(currentFile, path.extname(currentFile)))) {
                related.push(file);
            }
        }
        return [...new Set(related)];
    }
    // Exact text search
    async searchExact(query, options) {
        const results = [];
        const files = await this.getSearchFiles(options);
        for (const file of files) {
            const content = await this.getFileContent(file);
            if (!content)
                continue;
            const lines = content.split('\n');
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const index = line.toLowerCase().indexOf(query.toLowerCase());
                if (index !== -1) {
                    results.push({
                        file,
                        line: i + 1,
                        column: index + 1,
                        content: line.trim(),
                        context: this.getContext(lines, i, options.contextLines || 3),
                        relevanceScore: 1.0,
                        type: 'exact'
                    });
                }
            }
        }
        return results;
    }
    // Fuzzy search
    async searchFuzzy(query, options) {
        const results = [];
        const files = await this.getSearchFiles(options);
        for (const file of files) {
            const content = await this.getFileContent(file);
            if (!content)
                continue;
            const lines = content.split('\n');
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const similarity = this.calculateSimilarity(line.toLowerCase(), query.toLowerCase());
                if (similarity > (options.fuzzyThreshold || 0.6)) {
                    results.push({
                        file,
                        line: i + 1,
                        column: 1,
                        content: line.trim(),
                        context: this.getContext(lines, i, options.contextLines || 3),
                        relevanceScore: similarity,
                        type: 'fuzzy'
                    });
                }
            }
        }
        return results;
    }
    // Pattern search (regex)
    async searchPattern(query, options = {}) {
        const results = [];
        const files = await this.getSearchFiles(options);
        let regex;
        try {
            regex = new RegExp(query, 'gi');
        }
        catch {
            return results; // Invalid regex
        }
        for (const file of files) {
            const content = await this.getFileContent(file);
            if (!content)
                continue;
            const lines = content.split('\n');
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const matches = line.match(regex);
                if (matches) {
                    const index = line.search(regex);
                    results.push({
                        file,
                        line: i + 1,
                        column: index + 1,
                        content: line.trim(),
                        context: this.getContext(lines, i, options.contextLines || 3),
                        relevanceScore: 0.8,
                        type: 'pattern'
                    });
                }
            }
        }
        return results;
    }
    // Semantic search (placeholder for vector search integration)
    async searchSemantic(query, options) {
        // This would integrate with vector search capabilities
        // For now, return empty array
        return [];
    }
    // Initialize symbol index
    async initializeIndex() {
        const files = await this.getAllProjectFiles();
        for (const file of files) {
            await this.indexFile(file);
        }
        this.lastIndexTime = Date.now();
    }
    // Index a single file for symbols
    async indexFile(filePath) {
        const content = await this.getFileContent(filePath);
        if (!content)
            return;
        const symbols = [];
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            // Function declarations
            const functionMatch = line.match(/(?:function\s+|const\s+|let\s+|var\s+)(\w+)\s*(?:=\s*(?:async\s+)?(?:\([^)]*\)\s*)?=>|=\s*function|\()/);
            if (functionMatch) {
                symbols.push({
                    name: functionMatch[1],
                    type: 'function',
                    file: filePath,
                    line: i + 1,
                    scope: this.determineScope(lines, i),
                    signature: line.trim()
                });
            }
            // Class declarations
            const classMatch = line.match(/class\s+(\w+)/);
            if (classMatch) {
                symbols.push({
                    name: classMatch[1],
                    type: 'class',
                    file: filePath,
                    line: i + 1,
                    scope: 'global',
                    signature: line.trim()
                });
            }
            // Interface declarations
            const interfaceMatch = line.match(/interface\s+(\w+)/);
            if (interfaceMatch) {
                symbols.push({
                    name: interfaceMatch[1],
                    type: 'interface',
                    file: filePath,
                    line: i + 1,
                    scope: 'global',
                    signature: line.trim()
                });
            }
        }
        this.indexCache.set(filePath, symbols);
    }
    // Helper methods
    async ensureIndexFresh() {
        if (Date.now() - this.lastIndexTime > this.INDEX_REFRESH_INTERVAL) {
            await this.initializeIndex();
        }
    }
    async getSearchFiles(options) {
        const patterns = options.fileTypes?.map(ext => `**/*.${ext}`) || ['**/*.{ts,tsx,js,jsx}'];
        const ignore = ['node_modules/**', 'dist/**', 'out/**'];
        if (!options.includeTests) {
            ignore.push('**/*.test.*', '**/*.spec.*');
        }
        const files = [];
        for (const pattern of patterns) {
            const matches = await glob(pattern, {
                cwd: this.workspaceRoot,
                ignore,
                nodir: true
            });
            files.push(...matches);
        }
        return [...new Set(files)];
    }
    async getAllProjectFiles() {
        return this.getSearchFiles({ fileTypes: ['ts', 'tsx', 'js', 'jsx'] });
    }
    async getFileContent(filePath) {
        try {
            const fullPath = path.resolve(this.workspaceRoot, filePath);
            return await fs.readFile(fullPath, 'utf-8');
        }
        catch {
            return null;
        }
    }
    getContext(lines, lineIndex, contextLines) {
        const start = Math.max(0, lineIndex - contextLines);
        const end = Math.min(lines.length, lineIndex + contextLines + 1);
        return lines.slice(start, end);
    }
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        if (longer.length === 0)
            return 1.0;
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }
    levenshteinDistance(str1, str2) {
        const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
        for (let i = 0; i <= str1.length; i++)
            matrix[0][i] = i;
        for (let j = 0; j <= str2.length; j++)
            matrix[j][0] = j;
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(matrix[j][i - 1] + 1, matrix[j - 1][i] + 1, matrix[j - 1][i - 1] + indicator);
            }
        }
        return matrix[str2.length][str1.length];
    }
    deduplicateResults(results) {
        const seen = new Set();
        return results.filter(result => {
            const key = `${result.file}:${result.line}:${result.column}`;
            if (seen.has(key))
                return false;
            seen.add(key);
            return true;
        });
    }
    rankResults(results, query) {
        return results.sort((a, b) => {
            // Prioritize exact matches
            if (a.type === 'exact' && b.type !== 'exact')
                return -1;
            if (b.type === 'exact' && a.type !== 'exact')
                return 1;
            // Then by relevance score
            if (a.relevanceScore !== b.relevanceScore) {
                return b.relevanceScore - a.relevanceScore;
            }
            // Then by file name similarity to query
            const aFileScore = this.calculateSimilarity(path.basename(a.file), query);
            const bFileScore = this.calculateSimilarity(path.basename(b.file), query);
            return bFileScore - aFileScore;
        });
    }
    calculateSymbolRelevance(symbol, query) {
        const nameMatch = this.calculateSimilarity(symbol.name.toLowerCase(), query.toLowerCase());
        const typeBonus = symbol.type === 'function' ? 0.1 : 0;
        return nameMatch + typeBonus;
    }
    determineScope(lines, lineIndex) {
        // Simple scope detection - could be enhanced
        for (let i = lineIndex - 1; i >= 0; i--) {
            const line = lines[i];
            if (line.includes('class ')) {
                const match = line.match(/class\s+(\w+)/);
                return match ? match[1] : 'unknown';
            }
        }
        return 'global';
    }
    resolveImportPath(importPath, currentFile) {
        if (importPath.startsWith('./') || importPath.startsWith('../')) {
            const currentDir = path.dirname(currentFile);
            const resolved = path.resolve(this.workspaceRoot, currentDir, importPath);
            const relativePath = path.relative(this.workspaceRoot, resolved);
            // Try different extensions
            const extensions = ['.ts', '.tsx', '.js', '.jsx', '/index.ts', '/index.tsx', '/index.js', '/index.jsx'];
            for (const ext of extensions) {
                const fullPath = relativePath + ext;
                if (fs.pathExistsSync(path.resolve(this.workspaceRoot, fullPath))) {
                    return fullPath;
                }
            }
        }
        return null;
    }
}
exports.IntelligentSearch = IntelligentSearch;
//# sourceMappingURL=IntelligentSearch.js.map